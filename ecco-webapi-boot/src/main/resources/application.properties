
# NOTE: SEE ALSO: application.yml which is used for the discoverable entries

# This might need moving to -D params for JVM
user.timezone=UTC
java.locale.providers=JRE,SPI

ecco.api.basePath=/api
ecco.mvc.basePath=/nav
ecco.mvc.resourcesPath=/r


# TODO: Define/expose some beans so we can auto-complete our props
env=dev
liquibase=CREATE

log4j.configuration=log4j-dev.xml
# logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.transaction=TRACE
logging.level.org.hibernate.SQL=TRACE
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
# work out why there are bldg updates
logging.level.org.hibernate.event.def.DefaultFlushEntityEventListener=TRACE
